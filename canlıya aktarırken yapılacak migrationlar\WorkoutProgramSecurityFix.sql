-- Antrenman Programı Güvenlik Açığı Düzeltme Migration Script
-- WorkoutProgramDay ve WorkoutProgramExercise tablolarına CompanyID ekleme
-- Multi-tenant güvenlik açığını kapatma

USE [GymProject]
GO

PRINT 'Antrenman Programı Güvenlik Açığı Düzeltme başlatılıyor...'
GO

-- 1. BACKUP KONTROLÜ
PRINT 'UYARI: Bu migration kritik güvenlik açığını kapatır!'
PRINT 'Devam etmeden önce veritabanı backup alındığından emin olun!'
PRINT 'Migration başlatılıyor...'
GO

-- 2. WORKOUTPROGRAMDAYS TABLOSUNA COMPANYID EKLEME
PRINT 'WorkoutProgramDays tablosuna CompanyID ekleniyor...'

-- CompanyID kolonu ekle
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('WorkoutProgramDays') AND name = 'CompanyID')
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramDays] 
    ADD [CompanyID] int NOT NULL DEFAULT(0)
    
    PRINT 'WorkoutProgramDays.CompanyID kolonu eklendi.'
END
ELSE
BEGIN
    PRINT 'WorkoutProgramDays.CompanyID kolonu zaten mevcut.'
END
GO

-- DeletedDate kolonu ekle
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('WorkoutProgramDays') AND name = 'DeletedDate')
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramDays] 
    ADD [DeletedDate] datetime2(7) NULL
    
    PRINT 'WorkoutProgramDays.DeletedDate kolonu eklendi.'
END
GO

-- UpdatedDate kolonu ekle
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('WorkoutProgramDays') AND name = 'UpdatedDate')
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramDays] 
    ADD [UpdatedDate] datetime2(7) NULL
    
    PRINT 'WorkoutProgramDays.UpdatedDate kolonu eklendi.'
END
GO

-- 3. WORKOUTPROGRAMEXERCISES TABLOSUNA COMPANYID EKLEME
PRINT 'WorkoutProgramExercises tablosuna CompanyID ekleniyor...'

-- CompanyID kolonu ekle
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('WorkoutProgramExercises') AND name = 'CompanyID')
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramExercises] 
    ADD [CompanyID] int NOT NULL DEFAULT(0)
    
    PRINT 'WorkoutProgramExercises.CompanyID kolonu eklendi.'
END
ELSE
BEGIN
    PRINT 'WorkoutProgramExercises.CompanyID kolonu zaten mevcut.'
END
GO

-- DeletedDate kolonu ekle
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('WorkoutProgramExercises') AND name = 'DeletedDate')
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramExercises] 
    ADD [DeletedDate] datetime2(7) NULL
    
    PRINT 'WorkoutProgramExercises.DeletedDate kolonu eklendi.'
END
GO

-- UpdatedDate kolonu ekle
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('WorkoutProgramExercises') AND name = 'UpdatedDate')
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramExercises] 
    ADD [UpdatedDate] datetime2(7) NULL
    
    PRINT 'WorkoutProgramExercises.UpdatedDate kolonu eklendi.'
END
GO

-- 4. MEVCUT KAYITLARA COMPANYID ATAMA
PRINT 'Mevcut kayıtlara CompanyID değerleri atanıyor...'

-- WorkoutProgramDays için CompanyID güncelleme
UPDATE wpd 
SET wpd.CompanyID = wpt.CompanyID
FROM [dbo].[WorkoutProgramDays] wpd
INNER JOIN [dbo].[WorkoutProgramTemplates] wpt ON wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
WHERE wpd.CompanyID = 0

PRINT 'WorkoutProgramDays CompanyID değerleri güncellendi.'
GO

-- WorkoutProgramExercises için CompanyID güncelleme
UPDATE wpe 
SET wpe.CompanyID = wpt.CompanyID
FROM [dbo].[WorkoutProgramExercises] wpe
INNER JOIN [dbo].[WorkoutProgramDays] wpd ON wpe.WorkoutProgramDayID = wpd.WorkoutProgramDayID
INNER JOIN [dbo].[WorkoutProgramTemplates] wpt ON wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
WHERE wpe.CompanyID = 0

PRINT 'WorkoutProgramExercises CompanyID değerleri güncellendi.'
GO

-- 5. FOREIGN KEY CONSTRAINT'LERİ EKLEME
PRINT 'Foreign key constraint''leri ekleniyor...'

-- WorkoutProgramDays için Company FK
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_WorkoutProgramDays_Companies')
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramDays]
    ADD CONSTRAINT [FK_WorkoutProgramDays_Companies] 
    FOREIGN KEY ([CompanyID]) REFERENCES [dbo].[Companies]([CompanyID])
    
    PRINT 'WorkoutProgramDays Company FK eklendi.'
END
GO

-- WorkoutProgramExercises için Company FK
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_WorkoutProgramExercises_Companies')
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramExercises]
    ADD CONSTRAINT [FK_WorkoutProgramExercises_Companies] 
    FOREIGN KEY ([CompanyID]) REFERENCES [dbo].[Companies]([CompanyID])
    
    PRINT 'WorkoutProgramExercises Company FK eklendi.'
END
GO

-- 6. PERFORMANS İNDEXLERİ EKLEME
PRINT 'Performans indexleri ekleniyor...'

-- WorkoutProgramDays için CompanyID index
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_WorkoutProgramDays_CompanyID')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_WorkoutProgramDays_CompanyID]
    ON [dbo].[WorkoutProgramDays] ([CompanyID])
    INCLUDE ([WorkoutProgramTemplateID], [DayNumber])
    
    PRINT 'WorkoutProgramDays CompanyID index eklendi.'
END
GO

-- WorkoutProgramExercises için CompanyID index
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_WorkoutProgramExercises_CompanyID')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_WorkoutProgramExercises_CompanyID]
    ON [dbo].[WorkoutProgramExercises] ([CompanyID])
    INCLUDE ([WorkoutProgramDayID], [OrderIndex])
    
    PRINT 'WorkoutProgramExercises CompanyID index eklendi.'
END
GO

-- 7. GÜVENLİK KONTROLÜ
PRINT 'Güvenlik kontrolü yapılıyor...'

DECLARE @OrphanDays INT, @OrphanExercises INT

-- Yetim WorkoutProgramDays kontrolü
SELECT @OrphanDays = COUNT(*)
FROM [dbo].[WorkoutProgramDays] wpd
LEFT JOIN [dbo].[WorkoutProgramTemplates] wpt ON wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
WHERE wpd.CompanyID != wpt.CompanyID OR wpt.CompanyID IS NULL

-- Yetim WorkoutProgramExercises kontrolü
SELECT @OrphanExercises = COUNT(*)
FROM [dbo].[WorkoutProgramExercises] wpe
INNER JOIN [dbo].[WorkoutProgramDays] wpd ON wpe.WorkoutProgramDayID = wpd.WorkoutProgramDayID
WHERE wpe.CompanyID != wpd.CompanyID

IF @OrphanDays = 0 AND @OrphanExercises = 0
    PRINT 'Güvenlik kontrolü başarılı! Tüm kayıtlar doğru CompanyID''ye sahip.'
ELSE
    PRINT 'UYARI: ' + CAST(@OrphanDays AS VARCHAR(10)) + ' yetim gün, ' + CAST(@OrphanExercises AS VARCHAR(10)) + ' yetim egzersiz bulundu!'

-- 8. STATISTICS GÜNCELLEME
UPDATE STATISTICS [dbo].[WorkoutProgramDays]
UPDATE STATISTICS [dbo].[WorkoutProgramExercises]
PRINT 'Statistics güncellendi.'
GO

PRINT 'Antrenman Programı Güvenlik Açığı Düzeltme tamamlandı!'
PRINT 'Eklenen güvenlik önlemleri:'
PRINT '- WorkoutProgramDays.CompanyID kolonu ve FK constraint'
PRINT '- WorkoutProgramExercises.CompanyID kolonu ve FK constraint'
PRINT '- Performans indexleri'
PRINT '- Mevcut kayıtlara CompanyID ataması'
PRINT 'Sistem artık multi-tenant güvenli!'
GO
