using Core.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace Entities.Concrete
{
    public class WorkoutProgramExercise : ICompanyEntity
    {
        [Key]
        public int WorkoutProgramExerciseID { get; set; }
        public int WorkoutProgramDayID { get; set; }
        public int CompanyID { get; set; } // Multi-tenant güvenlik için eklendi
        public string ExerciseType { get; set; } // "System" veya "Company"
        public int ExerciseID { get; set; } // SystemExerciseID veya CompanyExerciseID
        public int OrderIndex { get; set; } // Egzersiz sırası (1, 2, 3...)
        public int Sets { get; set; } // Set sayısı
        public string Reps { get; set; } // Tekrar sayısı (12, MAX, 12-15 vb.)
        public int? RestTime { get; set; } // Dinlenme süresi (saniye)
        public string? Notes { get; set; } // Egzersiz notları
        public DateTime? CreationDate { get; set; }
        public DateTime? DeletedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
    }
}
