-- Üye Program Atama Sistemi Concurrency Fix Migration Script
-- Bu script concurrency problemini çözmek için ek constraint'ler ekler
-- Aynı üyeye aynı programın aynı anda birden fazla kez atanmasını engeller

USE [GymProject]
GO

PRINT 'Üye Program Atama Sistemi Concurrency Fix başlatılıyor...'
GO

-- 1. MEVCUT DUPLICATE KAYITLARI TEMİZLE (Eğer varsa)
PRINT 'Mevcut duplicate kayıtlar kontrol ediliyor...'

-- Aynı üye-program kombinasyonunda birden fazla aktif kayıt varsa, en son olanı hariç diğerlerini pasif yap
WITH DuplicateAssignments AS (
    SELECT 
        MemberWorkoutProgramID,
        MemberID,
        WorkoutProgramTemplateID,
        CompanyID,
        ROW_NUMBER() OVER (
            PARTITION BY MemberID, WorkoutProgramTemplateID, CompanyID 
            ORDER BY AssignedDate DESC, MemberWorkoutProgramID DESC
        ) as RowNum
    FROM MemberWorkoutPrograms 
    WHERE IsActive = 1
)
UPDATE mwp 
SET IsActive = 0, 
    DeletedDate = GETDATE(),
    UpdatedDate = GETDATE()
FROM MemberWorkoutPrograms mwp
INNER JOIN DuplicateAssignments da ON mwp.MemberWorkoutProgramID = da.MemberWorkoutProgramID
WHERE da.RowNum > 1

PRINT 'Duplicate kayıtlar temizlendi.'
GO

-- 2. UNIQUE CONSTRAINT EKLE (Concurrency koruması)
-- Aynı üyeye aynı programın aktif olarak sadece bir kez atanabilmesini sağla
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'UQ_MemberWorkoutPrograms_Member_Template_Active')
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX [UQ_MemberWorkoutPrograms_Member_Template_Active]
    ON [dbo].[MemberWorkoutPrograms] ([MemberID], [WorkoutProgramTemplateID], [CompanyID])
    WHERE [IsActive] = 1
    
    PRINT 'Unique constraint eklendi: UQ_MemberWorkoutPrograms_Member_Template_Active'
END
ELSE
BEGIN
    PRINT 'Unique constraint zaten mevcut: UQ_MemberWorkoutPrograms_Member_Template_Active'
END
GO

-- 3. PERFORMANS İYİLEŞTİRMESİ - Concurrency için ek indexler
-- Soft delete kontrolü için optimize edilmiş index
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MemberWorkoutPrograms_Concurrency_Check')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MemberWorkoutPrograms_Concurrency_Check]
    ON [dbo].[MemberWorkoutPrograms] ([MemberID], [WorkoutProgramTemplateID], [CompanyID], [IsActive])
    INCLUDE ([MemberWorkoutProgramID], [AssignedDate])
    
    PRINT 'Concurrency check index eklendi: IX_MemberWorkoutPrograms_Concurrency_Check'
END
ELSE
BEGIN
    PRINT 'Concurrency check index zaten mevcut: IX_MemberWorkoutPrograms_Concurrency_Check'
END
GO

-- 4. TRIGGER EKLE (Ek güvenlik katmanı)
-- Eğer unique constraint bypass edilmeye çalışılırsa trigger devreye girer
IF NOT EXISTS (SELECT * FROM sys.triggers WHERE name = 'TR_MemberWorkoutPrograms_PreventDuplicateActive')
BEGIN
    EXEC('
    CREATE TRIGGER [dbo].[TR_MemberWorkoutPrograms_PreventDuplicateActive]
    ON [dbo].[MemberWorkoutPrograms]
    FOR INSERT, UPDATE
    AS
    BEGIN
        SET NOCOUNT ON;
        
        -- Sadece IsActive = 1 olan kayıtlar için kontrol et
        IF EXISTS (
            SELECT 1 
            FROM inserted i
            WHERE i.IsActive = 1
        )
        BEGIN
            -- Aynı üye-program kombinasyonunda başka aktif kayıt var mı kontrol et
            IF EXISTS (
                SELECT 1
                FROM inserted i
                INNER JOIN MemberWorkoutPrograms existing ON 
                    i.MemberID = existing.MemberID 
                    AND i.WorkoutProgramTemplateID = existing.WorkoutProgramTemplateID
                    AND i.CompanyID = existing.CompanyID
                    AND existing.IsActive = 1
                    AND existing.MemberWorkoutProgramID != i.MemberWorkoutProgramID
            )
            BEGIN
                RAISERROR (''Bu üyeye bu program zaten aktif olarak atanmış. Önce mevcut atamanın aktifliğini kapatın.'', 16, 1)
                ROLLBACK TRANSACTION
                RETURN
            END
        END
    END
    ')
    
    PRINT 'Trigger eklendi: TR_MemberWorkoutPrograms_PreventDuplicateActive'
END
ELSE
BEGIN
    PRINT 'Trigger zaten mevcut: TR_MemberWorkoutPrograms_PreventDuplicateActive'
END
GO

-- 5. STATISTICS GÜNCELLE (Performans optimizasyonu)
UPDATE STATISTICS [dbo].[MemberWorkoutPrograms]
PRINT 'Statistics güncellendi.'
GO

-- 6. KONTROL SORGUSU
PRINT 'Concurrency fix kontrolü yapılıyor...'

DECLARE @DuplicateCount INT
SELECT @DuplicateCount = COUNT(*)
FROM (
    SELECT MemberID, WorkoutProgramTemplateID, CompanyID, COUNT(*) as ActiveCount
    FROM MemberWorkoutPrograms 
    WHERE IsActive = 1
    GROUP BY MemberID, WorkoutProgramTemplateID, CompanyID
    HAVING COUNT(*) > 1
) duplicates

IF @DuplicateCount = 0
    PRINT 'Concurrency fix başarılı! Duplicate aktif kayıt bulunamadı.'
ELSE
    PRINT 'UYARI: Hala ' + CAST(@DuplicateCount AS VARCHAR(10)) + ' duplicate aktif kayıt var!'

GO

PRINT 'Üye Program Atama Sistemi Concurrency Fix tamamlandı!'
PRINT 'Eklenen güvenlik önlemleri:'
PRINT '- Unique constraint (UQ_MemberWorkoutPrograms_Member_Template_Active)'
PRINT '- Concurrency check index (IX_MemberWorkoutPrograms_Concurrency_Check)'
PRINT '- Duplicate prevention trigger (TR_MemberWorkoutPrograms_PreventDuplicateActive)'
PRINT '- Mevcut duplicate kayıtlar temizlendi'
PRINT 'Sistem artık concurrency-safe!'
GO
